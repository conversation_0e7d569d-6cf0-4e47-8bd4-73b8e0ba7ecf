# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Spotify Music to SD Card
- **SEO关键词**: Spotify Music to SD Card
- **文章长度**: 1600字（可超出20%，最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: B - 修辞问句开头
- **推荐产品**: Cinch Audio Recorder Pro

## 需要执行的详细步骤清单

### 第1步：提取用户需求 ✅
- [x] 阅读并分析 info_aia.md 中的所有要求
- [x] 记录关键需求和限制条件
- [x] 确认推荐产品和写作指导原则

### 第2步：生成文章大纲
- [ ] 创建超级大纲 (super_outline.md)
  - [ ] 提取参考URL的H2-H4标题
  - [ ] 合并整理相似标题
  - [ ] 按层级结构重新组织
- [ ] 创建最终大纲 (final_outline.md)
  - [ ] 进行竞品内容空白分析
  - [ ] 挖掘独特价值点
  - [ ] 添加人工经验要素
  - [ ] 智能字数分配（1600字范围内）
  - [ ] 添加SEO关键词列表

### 第3步：创作初稿
- [ ] 基于最终大纲撰写完整文章
- [ ] 确保字数控制在1600-1920字范围
- [ ] 融入Cinch Audio Recorder推荐内容
- [ ] 保存为 first_draft.md

### 第4步：生成SEO内容
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image提示词
- [ ] 保存为 seo_metadata_images.md

### 第5步：质量检查
- [ ] 验证字数是否符合要求
- [ ] 检查拟人化写作风格
- [ ] 识别并修正AI语言痕迹
- [ ] 验证内链和外链有效性
- [ ] 确认相关图片添加

## 完成标准和检查点

### 内容质量标准
- **Effort**: 体现明显人工成分和深度思考
- **Originality**: 提供3-5个独特观点或解决方案
- **Talent/Skill**: 展示专业知识和实际经验
- **Accuracy**: 确保事实准确性

### 字数分配标准（基于1600字）
- Introduction: 128-160字 (8-10%)
- 核心推荐章节: 320-400字 (20-25%)
- 主要内容章节: 560-640字 (35-40%)
- 支撑章节: 400-480字 (25-30%)
- Conclusion + FAQ: 128-192字 (8-12%)

## 预期输出文件清单
1. `plan.md` - 执行计划文件 ✅
2. `super_outline.md` - 初级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 完整文章初稿
5. `seo_metadata_images.md` - SEO元数据和图片提示词

## 参考资源
- 参考URL: 5个竞品文章链接
- 产品指南: New_article/car_guide.md
- 写作风格: New_article/hl.md
- 工作流程: New_article/outline.md, first_draft.md, seo_titles.md
