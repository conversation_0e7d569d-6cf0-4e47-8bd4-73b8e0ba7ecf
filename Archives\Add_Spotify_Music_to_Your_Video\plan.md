# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Add Spotify Music to Your Video
- **SEO关键词**: Add Spotify Music to Your Video
- **文章长度**: 1600字（可超出20%，最多1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: B (修辞问句开头)

## 推荐产品
- **主要产品**: Cinch Audio Recorder Pro ($25.99)
- **产品页面**: https://www.cinchsolution.com/cinch-audio-recorder/
- **产品指南**: 参考 `New_article/car_guide.md`

## 内容质量要求
### 四大评估维度
1. **Effort (努力程度)**: 体现人工成分和深度思考
2. **Originality (原创性)**: 提供独特信息增量
3. **Talent/Skill (专业能力)**: 展示专业知识和实际经验
4. **Accuracy (准确性)**: 确保事实准确

### 信息增量要求
- 包含3-5个其他文章未涵盖的独特观点
- 基于实际使用经验的个人见解和试错故事
- 针对用户痛点的具体解决方案

## 执行步骤详细清单

### 步骤1: 基础"超级大纲"生成 ✅
- [x] 提取参考URL的H2-H4标题
- [x] 合并整理类似标题
- [x] 按层级结构组织框架大纲
- [x] 保存至 `super_outline.md`

### 步骤2: 创建最终文章大纲 ⏳
- [ ] 基于超级大纲优化结构
- [ ] 进行竞品内容空白分析
- [ ] 挖掘独特价值点
- [ ] 添加人工经验要素
- [ ] 智能字数分配
- [ ] 保存至 `final_outline.md`

### 步骤3: 创作初稿 ⏳
- [ ] 使用最终大纲撰写初稿
- [ ] 遵循拟人化写作要求
- [ ] 整合产品推荐内容
- [ ] 保存至 `first_draft.md`

### 步骤4: 生成SEO内容 ⏳
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image提示词
- [ ] 保存至 `seo_metadata_images.md`

### 步骤5: 质量检查 ⏳
- [ ] 检查字数是否在1600-1920字范围内
- [ ] 验证拟人化写作风格
- [ ] 检查AI语言和句子结构
- [ ] 验证内链和外链有效性
- [ ] 确认相关图片添加

## 完成标准和检查点

### 大纲阶段检查点
- [ ] 包含至少3个竞品文章未涵盖的独特观点
- [ ] 为每个H2章节准备人工经验要素
- [ ] 识别并准备解决用户具体痛点
- [ ] 包含可验证的准确信息和数据
- [ ] 体现作者专业判断和建议

### 初稿阶段检查点
- [ ] 字数控制在目标范围内
- [ ] 符合拟人化写作要求
- [ ] 产品推荐自然融入
- [ ] 内容结构清晰逻辑
- [ ] 包含实际使用案例

## 预期输出文件清单
1. `plan.md` - 执行计划文件 ✅
2. `super_outline.md` - 初级大纲
3. `final_outline.md` - 最终优化大纲
4. `first_draft.md` - 文章初稿
5. `seo_metadata_images.md` - SEO元数据和图片提示词

## 参考资源
- 用户需求文件: `New_article/info_aia.md`
- 产品指南: `New_article/car_guide.md`
- 大纲工作流程: `New_article/outline.md`
- 初稿工作流程: `New_article/first_draft.md`
- SEO工作流程: `New_article/seo_titles.md`
- 拟人化写作指南: `New_article/hl.md`

## 竞品分析来源
1. https://www.viwizard.com/spotify-music-tips/add-spotify-music-to-video.html
2. https://videoconverter.wondershare.com/audio/add-spotify-music-to-video.html
3. https://www.quora.com/What-is-the-easiest-way-to-use-songs-from-Spotify-in-an-iMovies-video
4. https://www.audifab.com/spotify-music-tips/add-spotify-to-capcut.html
5. https://www.drmare.com/spotify-music/add-spotify-music-to-video.html
