# 文章创作执行计划

## 用户需求和目标
- **文章主题**: Record Spotify Music with Audacity
- **SEO关键词**: Record Spotify Music with Audacity
- **文章长度**: 1600字（最多可超出20%，即1920字）
- **语言**: 英文
- **时间框架**: 基于2025年6月的最新数据和趋势
- **目标受众**: 音乐爱好者和创作者，专注于下载、编辑和分享音乐以获得更好的音频体验
- **开头策略**: B - 修辞问句开头
- **推荐产品**: Cinch Audio Recorder Pro

## 需要执行的详细步骤清单

### 第1步：基础研究和竞品分析
- [x] 提取参考URL内容
- [x] 分析竞品文章结构和覆盖范围
- [x] 识别内容空白和用户痛点
- [x] 收集独特价值点和实用建议

### 第2步：生成超级大纲
- [ ] 提取参考文章的H2-H4标题
- [ ] 合并整理类似标题
- [ ] 按层级结构重新组织
- [ ] 保存为super_outline.md

### 第3步：创建最终大纲
- [ ] 基于超级大纲优化结构
- [ ] 添加字数分配
- [ ] 包含SEO关键词列表
- [ ] 保存为final_outline.md

### 第4步：撰写初稿
- [ ] 基于最终大纲撰写内容
- [ ] 确保人工经验要素
- [ ] 集成Cinch Audio Recorder推荐
- [ ] 保存为first_draft.md

### 第5步：生成SEO内容
- [ ] 创建SEO标题和元描述
- [ ] 生成featured image提示词
- [ ] 保存为seo_metadata_images.md

## 每个步骤的完成标准

### 超级大纲标准
- 提取所有参考文章的主要标题
- 合并相似内容并标记源数量
- 形成清晰的层级结构

### 最终大纲标准
- 包含Introduction、Conclusion、FAQ
- 每个标题标注字数分配
- 总字数控制在1600-1920字范围内
- 包含至少3个独特价值点

### 初稿标准
- 严格按照字数分配撰写
- 体现人工经验和个人见解
- 自然集成产品推荐
- 符合拟人化写作要求

## 预期输出文件清单
1. super_outline.md - 基础超级大纲
2. final_outline.md - 最终优化大纲
3. first_draft.md - 完整初稿
4. seo_metadata_images.md - SEO相关内容

## 质量控制检查点
- [ ] 字数精确控制在要求范围内
- [ ] 每段符合拟人化写作要求
- [ ] 无明显AI语言和句子结构
- [ ] 内链外链按要求添加且有效
- [ ] 相关图片适当添加
- [ ] 产品推荐自然融入
